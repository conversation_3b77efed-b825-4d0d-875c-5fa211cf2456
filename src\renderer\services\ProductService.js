/**
 * 前端商品服务
 * 负责与后端 API 通信和商品数据处理
 */
class ProductService {
  constructor() {
    this.api = window.electronAPI;
  }

  /**
   * 获取所有商品
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>}
   */
  async getAllProducts(options = {}) {
    try {
      const result = await this.api.products.getAll(options);
      return result;
    } catch (error) {
      console.error('获取所有商品失败:', error);
      return {
        success: false,
        message: error.message,
        products: []
      };
    }
  }

  /**
   * 根据ID获取商品
   * @param {string} productId - 商品ID
   * @returns {Promise<Object>}
   */
  async getProductById(productId) {
    try {
      const result = await this.api.products.getById(productId);
      return result;
    } catch (error) {
      console.error('获取商品失败:', error);
      return {
        success: false,
        message: error.message,
        product: null
      };
    }
  }

  /**
   * 搜索商品
   * @param {Object} criteria - 搜索条件
   * @returns {Promise<Object>}
   */
  async searchProducts(criteria = {}) {
    try {
      const result = await this.api.products.search(criteria);
      return result;
    } catch (error) {
      console.error('搜索商品失败:', error);
      return {
        success: false,
        message: error.message,
        products: [],
        totalCount: 0,
        hasMore: false
      };
    }
  }

  /**
   * 创建商品
   * @param {Object} productData - 商品数据
   * @returns {Promise<Object>}
   */
  async createProduct(productData) {
    try {
      // 前端数据验证
      const validation = this.validateProductData(productData);
      if (!validation.isValid) {
        return {
          success: false,
          message: validation.errors.join(', '),
          product: null
        };
      }

      const result = await this.api.products.add(productData);
      return result;
    } catch (error) {
      console.error('创建商品失败:', error);
      return {
        success: false,
        message: error.message,
        product: null
      };
    }
  }

  /**
   * 更新商品
   * @param {Object} productData - 商品数据
   * @returns {Promise<Object>}
   */
  async updateProduct(productData) {
    try {
      // 前端数据验证
      const validation = this.validateProductData(productData);
      if (!validation.isValid) {
        return {
          success: false,
          message: validation.errors.join(', '),
          product: null
        };
      }

      const result = await this.api.products.update(productData);
      return result;
    } catch (error) {
      console.error('更新商品失败:', error);
      return {
        success: false,
        message: error.message,
        product: null
      };
    }
  }

  /**
   * 删除商品
   * @param {string} productId - 商品ID
   * @returns {Promise<Object>}
   */
  async deleteProduct(productId) {
    try {
      const result = await this.api.products.delete(productId);
      return result;
    } catch (error) {
      console.error('删除商品失败:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * 获取商品分类列表
   * @returns {Promise<Object>}
   */
  async getProductCategories() {
    try {
      const result = await this.api.products.getCategories();
      return result;
    } catch (error) {
      console.error('获取商品分类失败:', error);
      return {
        success: false,
        message: error.message,
        categories: []
      };
    }
  }

  /**
   * 获取商品统计信息
   * @returns {Promise<Object>}
   */
  async getProductStatistics() {
    try {
      const result = await this.api.products.getStatistics();
      return result;
    } catch (error) {
      console.error('获取商品统计失败:', error);
      return {
        success: false,
        message: error.message,
        statistics: {
          totalCount: 0,
          categoryCount: 0
        }
      };
    }
  }

  /**
   * 验证商品数据
   * @param {Object} productData - 商品数据
   * @returns {Object} 验证结果
   */
  validateProductData(productData) {
    const errors = [];

    if (!productData.name || productData.name.trim() === '') {
      errors.push('商品名称不能为空');
    }

    if (productData.name && productData.name.length > 100) {
      errors.push('商品名称不能超过100个字符');
    }

    if (productData.category && productData.category.length > 50) {
      errors.push('商品分类不能超过50个字符');
    }

    if (productData.price !== undefined && productData.price !== null && productData.price !== '') {
      const price = parseFloat(productData.price);
      if (isNaN(price) || price < 0) {
        errors.push('商品价格必须是非负数');
      }
    }

    if (productData.description && productData.description.length > 500) {
      errors.push('商品描述不能超过500个字符');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 生成商品ID
   * @returns {string}
   */
  generateProductId() {
    const timestamp = new Date().getTime();
    const random = Math.floor(Math.random() * 1000);
    return `PROD-${timestamp}-${random}`;
  }

  /**
   * 格式化价格显示
   * @param {number} price - 价格
   * @returns {string} 格式化后的价格字符串
   */
  formatPrice(price) {
    if (price === null || price === undefined) {
      return '未设置';
    }
    return `¥${parseFloat(price).toFixed(2)}`;
  }

  /**
   * 清理商品数据
   * @param {Object} productData - 原始商品数据
   * @returns {Object} 清理后的商品数据
   */
  cleanProductData(productData) {
    const cleaned = {
      name: productData.name ? productData.name.trim() : '',
      category: productData.category ? productData.category.trim() : '',
      description: productData.description ? productData.description.trim() : ''
    };

    // 处理价格
    if (productData.price !== undefined && productData.price !== null && productData.price !== '') {
      const price = parseFloat(productData.price);
      if (!isNaN(price) && price >= 0) {
        cleaned.price = price;
      }
    }

    return cleaned;
  }

  /**
   * 导出商品为CSV
   * @param {Array} products - 商品列表
   * @returns {Promise<Object>}
   */
  async exportProductsToCSV(products) {
    try {
      if (!products || products.length === 0) {
        return {
          success: false,
          message: '没有可导出的商品数据'
        };
      }

      const csvContent = this.generateCSVContent(products);
      const result = await this.api.files.exportCSV(csvContent);
      return result;
    } catch (error) {
      console.error('导出CSV失败:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * 生成CSV内容
   * @param {Array} products - 商品列表
   * @returns {string} CSV内容
   */
  generateCSVContent(products) {
    let csvContent = '商品ID,商品名称,商品分类,价格,描述,创建时间,更新时间\n';

    products.forEach(product => {
      const row = [
        product.id,
        product.name,
        product.category || '',
        product.price !== null ? product.price : '',
        product.description || '',
        product.createdAt || '',
        product.updatedAt || ''
      ];

      // 处理CSV中的特殊字符
      const processedRow = row.map(cell => {
        if (cell === null || cell === undefined) return '';
        const cellStr = String(cell);
        // 如果包含逗号、双引号或换行符，则用双引号包裹并将内部的双引号替换为两个双引号
        if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
          return `"${cellStr.replace(/"/g, '""')}"`;
        }
        return cellStr;
      });

      csvContent += processedRow.join(',') + '\n';
    });

    return csvContent;
  }
}

// 导出单例实例
window.productService = new ProductService();
