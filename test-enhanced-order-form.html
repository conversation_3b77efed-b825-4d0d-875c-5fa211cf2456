<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>增强版订单表单测试</title>
  <!-- Tailwind CSS 通过 CDN 引入 -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-100 p-8">
  <div class="max-w-6xl mx-auto">
    <h1 class="text-3xl font-bold text-gray-900 mb-8 text-center">增强版订单表单界面测试</h1>
    
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex justify-between items-center mb-6">
        <h1 id="order-form-title" class="text-2xl font-bold text-gray-900">新建订单</h1>
        <div class="flex space-x-3">
          <button id="save-order-btn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            保存订单
          </button>
          <button id="cancel-order-btn" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            取消
          </button>
        </div>
      </div>

      <!-- 订单基本信息 -->
      <div class="mb-6">
        <div class="order-info-card">
          <h3 class="order-info-card-title">订单基本信息</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="order-date" class="block text-sm font-medium text-gray-700 mb-2">订单日期 *</label>
              <input type="date" id="order-date" class="form-control" required>
            </div>
          </div>
        </div>
      </div>

      <!-- 客户信息区域 -->
      <div class="mb-6">
        <div class="customer-info-card">
          <h3 class="customer-info-card-title">
            <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            客户信息
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="customer-name" class="block text-sm font-medium text-gray-700 mb-2">客户名称 *</label>
              <input type="text" id="customer-name" class="form-control" placeholder="请输入客户名称" required>
            </div>
            <div>
              <label for="customer-contact" class="block text-sm font-medium text-gray-700 mb-2">联系方式</label>
              <input type="text" id="customer-contact" class="form-control" placeholder="请输入客户联系方式">
            </div>
            <div class="md:col-span-2">
              <label for="customer-address" class="block text-sm font-medium text-gray-700 mb-2">客户地址</label>
              <textarea id="customer-address" rows="3" class="form-control" placeholder="请输入客户详细地址，包括省市区县、街道门牌号等"></textarea>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间商信息区域 -->
      <div class="mb-6">
        <div class="intermediary-info-card">
          <h3 class="intermediary-info-card-title">
            <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            中间商信息
            <span class="text-sm font-normal text-gray-500 ml-2">(选填)</span>
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="intermediary-name" class="block text-sm font-medium text-gray-700 mb-2">中间商名称</label>
              <input type="text" id="intermediary-name" class="form-control" placeholder="请输入中间商名称">
            </div>
            <div>
              <label for="intermediary-contact" class="block text-sm font-medium text-gray-700 mb-2">联系方式</label>
              <input type="text" id="intermediary-contact" class="form-control" placeholder="请输入中间商联系方式">
            </div>
          </div>
        </div>
      </div>

      <!-- 材料项列表 -->
      <div class="mb-6">
        <div class="materials-info-card">
          <div class="flex justify-between items-center mb-4">
            <h3 class="materials-info-card-title">
              <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
              材料清单
            </h3>
            <button id="add-item-btn" type="button" class="add-material-btn-enhanced">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              添加材料项
            </button>
          </div>

          <!-- 材料项表头 -->
          <div class="grid grid-cols-12 gap-4 mb-3 text-sm font-medium text-gray-700 border-b border-gray-200 pb-2">
            <div class="col-span-3">材料名称</div>
            <div class="col-span-2">类型</div>
            <div class="col-span-2 text-center">数量</div>
            <div class="col-span-2 text-center">单价 (¥)</div>
            <div class="col-span-2 text-center">金额 (¥)</div>
            <div class="col-span-1 text-center">操作</div>
          </div>

          <!-- 材料项容器 -->
          <div id="order-items-container" class="space-y-3">
            <!-- 示例材料项 -->
            <div class="grid grid-cols-12 gap-4 mb-3 items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
              <div class="col-span-3">
                <input type="text" class="form-control item-name" placeholder="输入材料名称" value="水泥" required>
              </div>
              <div class="col-span-2 relative">
                <input type="text" class="form-control item-type" placeholder="选择或输入类型" value="建筑材料">
              </div>
              <div class="col-span-2">
                <input type="number" class="form-control item-quantity text-center" value="10" min="0" step="1" required>
              </div>
              <div class="col-span-2">
                <input type="number" class="form-control item-price text-center" value="25.50" min="0" step="0.01" required>
              </div>
              <div class="col-span-2 text-right">
                <span class="item-amount text-lg font-semibold text-gray-900">¥255.00</span>
              </div>
              <div class="col-span-1 text-center">
                <button type="button" class="delete-item p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 订单备注 -->
      <div class="mb-6">
        <div class="notes-info-card">
          <h3 class="notes-info-card-title">
            <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            订单备注
            <span class="text-sm font-normal text-gray-500 ml-2">(选填)</span>
          </h3>
          <textarea id="order-notes" rows="3" class="form-control" placeholder="请输入订单备注信息，如特殊要求、交货时间等"></textarea>
        </div>
      </div>

      <!-- 订单总计 -->
      <div class="order-total-section">
        <div class="order-total-display">
          <span class="order-total-label">
            <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
            订单总金额：
          </span>
          <span id="order-total-amount" class="order-total-amount">¥255.00</span>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 设置默认日期
    document.getElementById('order-date').value = new Date().toISOString().split('T')[0];
    
    // 简单的交互效果演示
    document.querySelectorAll('.form-control').forEach(input => {
      input.addEventListener('focus', function() {
        this.parentElement.style.transform = 'scale(1.02)';
      });
      
      input.addEventListener('blur', function() {
        this.parentElement.style.transform = 'scale(1)';
      });
    });
  </script>
</body>
</html>
