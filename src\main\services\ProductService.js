const ProductRepository = require('../database/repositories/ProductRepository');

/**
 * 商品服务类
 * 负责商品相关的业务逻辑
 */
class ProductService {
  /**
   * 获取所有商品
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>}
   */
  async getAllProducts(options = {}) {
    try {
      const products = await ProductRepository.findAll(options);
      
      return {
        success: true,
        products: products.map(product => product.toJSON()),
        message: '获取商品列表成功'
      };
    } catch (error) {
      console.error('获取商品列表失败:', error);
      return {
        success: false,
        message: `获取商品列表失败: ${error.message}`,
        products: []
      };
    }
  }

  /**
   * 根据ID获取商品
   * @param {string} productId - 商品ID
   * @returns {Promise<Object>}
   */
  async getProductById(productId) {
    try {
      if (!productId) {
        return {
          success: false,
          message: '商品ID不能为空',
          product: null
        };
      }

      const product = await ProductRepository.findById(productId);
      
      if (!product) {
        return {
          success: false,
          message: '商品不存在',
          product: null
        };
      }

      return {
        success: true,
        product: product.toJSON(),
        message: '获取商品成功'
      };
    } catch (error) {
      console.error('获取商品失败:', error);
      return {
        success: false,
        message: `获取商品失败: ${error.message}`,
        product: null
      };
    }
  }

  /**
   * 搜索商品
   * @param {Object} criteria - 搜索条件
   * @returns {Promise<Object>}
   */
  async searchProducts(criteria = {}) {
    try {
      const result = await ProductRepository.search(criteria);
      
      return {
        success: true,
        products: result.products.map(product => product.toJSON()),
        totalCount: result.totalCount,
        hasMore: result.hasMore,
        message: '搜索商品成功'
      };
    } catch (error) {
      console.error('搜索商品失败:', error);
      return {
        success: false,
        message: `搜索商品失败: ${error.message}`,
        products: [],
        totalCount: 0,
        hasMore: false
      };
    }
  }

  /**
   * 创建商品
   * @param {Object} productData - 商品数据
   * @returns {Promise<Object>}
   */
  async createProduct(productData) {
    try {
      // 验证必填字段
      if (!productData.name || productData.name.trim() === '') {
        return {
          success: false,
          message: '商品名称不能为空',
          product: null
        };
      }

      // 检查商品名称是否已存在
      const nameExists = await ProductRepository.isNameExists(productData.name.trim());
      if (nameExists) {
        return {
          success: false,
          message: '商品名称已存在，请使用其他名称',
          product: null
        };
      }

      // 清理和验证数据
      const cleanedData = this.cleanProductData(productData);
      
      // 创建商品
      const product = await ProductRepository.create(cleanedData);
      
      return {
        success: true,
        product: product.toJSON(),
        message: '商品创建成功'
      };
    } catch (error) {
      console.error('创建商品失败:', error);
      return {
        success: false,
        message: `创建商品失败: ${error.message}`,
        product: null
      };
    }
  }

  /**
   * 更新商品
   * @param {string} productId - 商品ID
   * @param {Object} productData - 商品数据
   * @returns {Promise<Object>}
   */
  async updateProduct(productId, productData) {
    try {
      if (!productId) {
        return {
          success: false,
          message: '商品ID不能为空',
          product: null
        };
      }

      // 验证必填字段
      if (!productData.name || productData.name.trim() === '') {
        return {
          success: false,
          message: '商品名称不能为空',
          product: null
        };
      }

      // 检查商品名称是否已存在（排除当前商品）
      const nameExists = await ProductRepository.isNameExists(productData.name.trim(), productId);
      if (nameExists) {
        return {
          success: false,
          message: '商品名称已存在，请使用其他名称',
          product: null
        };
      }

      // 清理和验证数据
      const cleanedData = this.cleanProductData(productData);
      
      // 更新商品
      const product = await ProductRepository.update(productId, cleanedData);
      
      return {
        success: true,
        product: product.toJSON(),
        message: '商品更新成功'
      };
    } catch (error) {
      console.error('更新商品失败:', error);
      return {
        success: false,
        message: `更新商品失败: ${error.message}`,
        product: null
      };
    }
  }

  /**
   * 删除商品
   * @param {string} productId - 商品ID
   * @returns {Promise<Object>}
   */
  async deleteProduct(productId) {
    try {
      if (!productId) {
        return {
          success: false,
          message: '商品ID不能为空'
        };
      }

      const deleted = await ProductRepository.delete(productId);
      
      if (!deleted) {
        return {
          success: false,
          message: '商品不存在或已被删除'
        };
      }

      return {
        success: true,
        message: '商品删除成功'
      };
    } catch (error) {
      console.error('删除商品失败:', error);
      return {
        success: false,
        message: `删除商品失败: ${error.message}`
      };
    }
  }

  /**
   * 获取商品分类列表
   * @returns {Promise<Object>}
   */
  async getProductCategories() {
    try {
      const categories = await ProductRepository.getCategories();
      
      return {
        success: true,
        categories,
        message: '获取商品分类成功'
      };
    } catch (error) {
      console.error('获取商品分类失败:', error);
      return {
        success: false,
        message: `获取商品分类失败: ${error.message}`,
        categories: []
      };
    }
  }

  /**
   * 获取商品统计信息
   * @returns {Promise<Object>}
   */
  async getProductStatistics() {
    try {
      const totalCount = await ProductRepository.count();
      const categories = await ProductRepository.getCategories();
      
      return {
        success: true,
        statistics: {
          totalCount,
          categoryCount: categories.length
        },
        message: '获取商品统计成功'
      };
    } catch (error) {
      console.error('获取商品统计失败:', error);
      return {
        success: false,
        message: `获取商品统计失败: ${error.message}`,
        statistics: {
          totalCount: 0,
          categoryCount: 0
        }
      };
    }
  }

  /**
   * 清理商品数据
   * @param {Object} productData - 原始商品数据
   * @returns {Object} 清理后的商品数据
   */
  cleanProductData(productData) {
    const cleaned = {
      name: productData.name ? productData.name.trim() : '',
      category: productData.category ? productData.category.trim() : null,
      description: productData.description ? productData.description.trim() : null
    };

    // 处理价格
    if (productData.price !== undefined && productData.price !== null && productData.price !== '') {
      const price = parseFloat(productData.price);
      if (!isNaN(price) && price >= 0) {
        cleaned.price = price;
      } else {
        cleaned.price = null;
      }
    } else {
      cleaned.price = null;
    }

    return cleaned;
  }
}

module.exports = new ProductService();
