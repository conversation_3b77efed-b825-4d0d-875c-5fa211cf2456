/**
 * 前端订单服务
 * 负责与后端 API 通信和数据处理
 */
class OrderService {
  constructor() {
    this.api = window.electronAPI;
  }

  /**
   * 获取所有订单
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>}
   */
  async getAllOrders(options = {}) {
    try {
      const result = await this.api.orders.getAll(options);
      return result;
    } catch (error) {
      console.error('获取所有订单失败:', error);
      return {
        success: false,
        message: error.message,
        orders: []
      };
    }
  }

  /**
   * 根据ID获取订单
   * @param {string} orderId - 订单ID
   * @returns {Promise<Object>}
   */
  async getOrderById(orderId) {
    try {
      const result = await this.api.orders.getById(orderId);
      return result;
    } catch (error) {
      console.error('获取订单失败:', error);
      return {
        success: false,
        message: error.message,
        order: null
      };
    }
  }

  /**
   * 搜索订单
   * @param {Object} criteria - 搜索条件
   * @returns {Promise<Object>}
   */
  async searchOrders(criteria = {}) {
    try {
      const result = await this.api.orders.search(criteria);
      return result;
    } catch (error) {
      console.error('搜索订单失败:', error);
      return {
        success: false,
        message: error.message,
        orders: [],
        totalCount: 0
      };
    }
  }

  /**
   * 创建订单
   * @param {Object} orderData - 订单数据
   * @returns {Promise<Object>}
   */
  async createOrder(orderData) {
    try {
      // 前端数据验证
      const validation = this.validateOrderData(orderData);
      if (!validation.isValid) {
        return {
          success: false,
          message: validation.errors.join(', '),
          order: null
        };
      }

      const result = await this.api.orders.add(orderData);
      return result;
    } catch (error) {
      console.error('创建订单失败:', error);
      return {
        success: false,
        message: error.message,
        order: null
      };
    }
  }

  /**
   * 更新订单
   * @param {Object} orderData - 订单数据
   * @returns {Promise<Object>}
   */
  async updateOrder(orderData) {
    try {
      // 前端数据验证
      const validation = this.validateOrderData(orderData);
      if (!validation.isValid) {
        return {
          success: false,
          message: validation.errors.join(', '),
          order: null
        };
      }

      const result = await this.api.orders.update(orderData);
      return result;
    } catch (error) {
      console.error('更新订单失败:', error);
      return {
        success: false,
        message: error.message,
        order: null
      };
    }
  }

  /**
   * 删除订单
   * @param {string} orderId - 订单ID
   * @returns {Promise<Object>}
   */
  async deleteOrder(orderId) {
    try {
      const result = await this.api.orders.delete(orderId);
      return result;
    } catch (error) {
      console.error('删除订单失败:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * 获取订单统计信息
   * @param {Object} criteria - 统计条件
   * @returns {Promise<Object>}
   */
  async getOrderStatistics(criteria = {}) {
    try {
      const result = await this.api.orders.getStatistics(criteria);
      return result;
    } catch (error) {
      console.error('获取订单统计失败:', error);
      return {
        success: false,
        message: error.message,
        statistics: {
          orderCount: 0,
          totalAmount: 0
        }
      };
    }
  }

  /**
   * 获取今日统计
   * @returns {Promise<Object>}
   */
  async getTodayStatistics() {
    try {
      const result = await this.api.orders.getTodayStatistics();
      return result;
    } catch (error) {
      console.error('获取今日统计失败:', error);
      return {
        success: false,
        message: error.message,
        statistics: {
          orderCount: 0,
          totalAmount: 0
        }
      };
    }
  }

  /**
   * 导出订单为CSV
   * @param {Array} orders - 订单列表
   * @returns {Promise<Object>}
   */
  async exportOrdersToCSV(orders) {
    try {
      if (!orders || orders.length === 0) {
        return {
          success: false,
          message: '没有可导出的订单数据'
        };
      }

      const csvContent = this.generateCSVContent(orders);
      const result = await this.api.files.exportCSV(csvContent);
      return result;
    } catch (error) {
      console.error('导出CSV失败:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * 生成CSV内容
   * @param {Array} orders - 订单列表
   * @returns {string} CSV内容
   */
  generateCSVContent(orders) {
    let csvContent = '订单ID,日期,客户名称,客户联系方式,中间商名称,中间商联系方式,材料名称,材料类型,数量,单价,金额,订单总金额,备注\n';

    orders.forEach(order => {
      order.items.forEach((item, index) => {
        const row = [
          order.id,
          order.date,
          order.customerName,
          order.customerContact || '',
          order.intermediaryName || '',
          order.intermediaryContact || '',
          item.name,
          item.type || '',
          item.quantity,
          item.price,
          item.amount,
          index === 0 ? order.totalAmount : '', // 只在第一行显示总金额
          index === 0 ? (order.notes || '') : '' // 只在第一行显示备注
        ];

        // 处理CSV中的特殊字符
        const processedRow = row.map(cell => {
          if (cell === null || cell === undefined) return '';
          const cellStr = String(cell);
          // 如果包含逗号、双引号或换行符，则用双引号包裹并将内部的双引号替换为两个双引号
          if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
            return `"${cellStr.replace(/"/g, '""')}"`;
          }
          return cellStr;
        });

        csvContent += processedRow.join(',') + '\n';
      });
    });

    return csvContent;
  }

  /**
   * 验证订单数据
   * @param {Object} orderData - 订单数据
   * @returns {Object} 验证结果
   */
  validateOrderData(orderData) {
    const errors = [];

    if (!orderData.date) {
      errors.push('订单日期不能为空');
    }

    if (!orderData.customerName || orderData.customerName.trim() === '') {
      errors.push('客户名称不能为空');
    }

    if (!orderData.items || !Array.isArray(orderData.items) || orderData.items.length === 0) {
      errors.push('订单必须包含至少一个材料项');
    } else {
      orderData.items.forEach((item, index) => {
        if (!item.name || item.name.trim() === '') {
          errors.push(`第${index + 1}个材料项名称不能为空`);
        }
        if (!item.quantity || item.quantity <= 0) {
          errors.push(`第${index + 1}个材料项数量必须大于0`);
        }
        if (!item.price || item.price <= 0) {
          errors.push(`第${index + 1}个材料项单价必须大于0`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 生成订单ID
   * @returns {string}
   */
  generateOrderId() {
    const timestamp = new Date().getTime();
    const random = Math.floor(Math.random() * 1000);
    return `ORD-${timestamp}-${random}`;
  }

  /**
   * 根据材料名称搜索相关的材料类型
   * @param {string} materialName - 材料名称
   * @returns {Promise<Object>}
   */
  async searchMaterialTypes(materialName) {
    try {
      const result = await this.api.orders.searchMaterialTypes(materialName);
      return result;
    } catch (error) {
      console.error('搜索材料类型失败:', error);
      return {
        success: false,
        message: error.message,
        types: []
      };
    }
  }

  /**
   * 获取所有材料类型
   * @returns {Promise<Object>}
   */
  async getAllMaterialTypes() {
    try {
      const result = await this.api.orders.getAllMaterialTypes();
      return result;
    } catch (error) {
      console.error('获取所有材料类型失败:', error);
      return {
        success: false,
        message: error.message,
        types: []
      };
    }
  }
}

// 导出单例实例
window.orderService = new OrderService();
