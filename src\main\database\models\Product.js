const dbConnection = require('../connection');

/**
 * 商品数据模型
 * 负责商品相关的数据库操作
 */
class Product {
  constructor(data = {}) {
    this.id = data.id || null;
    this.name = data.name || null;
    this.category = data.category || null;
    this.price = data.price || null;
    this.description = data.description || null;
    this.createdAt = data.createdAt || data.created_at || null;
    this.updatedAt = data.updatedAt || data.updated_at || null;
  }

  /**
   * 验证商品数据
   * @returns {Object} 验证结果
   */
  validate() {
    const errors = [];

    if (!this.name || this.name.trim() === '') {
      errors.push('商品名称不能为空');
    }

    if (this.name && this.name.length > 100) {
      errors.push('商品名称不能超过100个字符');
    }

    if (this.category && this.category.length > 50) {
      errors.push('商品分类不能超过50个字符');
    }

    if (this.price !== null && this.price !== undefined) {
      if (isNaN(this.price) || this.price < 0) {
        errors.push('商品价格必须是非负数');
      }
    }

    if (this.description && this.description.length > 500) {
      errors.push('商品描述不能超过500个字符');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 保存商品到数据库
   * @returns {Promise<Product>}
   */
  async save() {
    const validation = this.validate();
    if (!validation.isValid) {
      throw new Error(`商品数据验证失败: ${validation.errors.join(', ')}`);
    }

    try {
      await dbConnection.beginTransaction();

      if (this.id) {
        // 更新现有商品
        await this.update();
      } else {
        // 创建新商品
        await this.create();
      }

      await dbConnection.commit();
      return this;
    } catch (error) {
      await dbConnection.rollback();
      throw error;
    }
  }

  /**
   * 创建新商品
   * @returns {Promise<void>}
   */
  async create() {
    // 生成商品ID
    this.id = this.generateProductId();

    // 插入商品
    const sql = `
      INSERT INTO products (id, name, category, price, description)
      VALUES (?, ?, ?, ?, ?)
    `;

    await dbConnection.run(sql, [
      this.id,
      this.name,
      this.category,
      this.price,
      this.description
    ]);
  }

  /**
   * 更新现有商品
   * @returns {Promise<void>}
   */
  async update() {
    const sql = `
      UPDATE products SET
        name = ?, category = ?, price = ?, description = ?
      WHERE id = ?
    `;

    await dbConnection.run(sql, [
      this.name,
      this.category,
      this.price,
      this.description,
      this.id
    ]);
  }

  /**
   * 删除商品
   * @returns {Promise<void>}
   */
  async delete() {
    if (!this.id) {
      throw new Error('无法删除未保存的商品');
    }

    const sql = 'DELETE FROM products WHERE id = ?';
    await dbConnection.run(sql, [this.id]);
  }

  /**
   * 生成商品ID
   * @returns {string}
   */
  generateProductId() {
    const timestamp = new Date().getTime();
    const random = Math.floor(Math.random() * 1000);
    return `PROD-${timestamp}-${random}`;
  }

  /**
   * 转换为JSON对象
   * @returns {Object}
   */
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      category: this.category,
      price: this.price,
      description: this.description,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * 从数据库记录创建Product实例
   * @param {Object} record - 数据库记录
   * @returns {Product}
   */
  static fromRecord(record) {
    return new Product({
      id: record.id,
      name: record.name,
      category: record.category,
      price: record.price,
      description: record.description,
      createdAt: record.created_at,
      updatedAt: record.updated_at
    });
  }
}

module.exports = Product;
