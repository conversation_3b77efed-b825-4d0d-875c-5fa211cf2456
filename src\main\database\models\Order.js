const dbConnection = require('../connection');

/**
 * 订单数据模型
 * 负责订单相关的数据库操作
 */
class Order {
  constructor(data = {}) {
    this.id = data.id || null;
    this.date = data.date || null;
    this.customerName = data.customerName || data.customer_name || null;
    this.customerContact = data.customerContact || data.customer_contact || null;
    this.customerAddress = data.customerAddress || data.customer_address || null;
    this.intermediaryName = data.intermediaryName || data.intermediary_name || null;
    this.intermediaryContact = data.intermediaryContact || data.intermediary_contact || null;
    this.notes = data.notes || null;
    this.totalAmount = data.totalAmount || data.total_amount || 0;
    this.items = data.items || [];
    this.createdAt = data.createdAt || data.created_at || null;
    this.updatedAt = data.updatedAt || data.updated_at || null;
  }

  /**
   * 验证订单数据
   * @returns {Object} 验证结果
   */
  validate() {
    const errors = [];

    if (!this.date) {
      errors.push('订单日期不能为空');
    }

    if (!this.customerName || this.customerName.trim() === '') {
      errors.push('客户名称不能为空');
    }

    if (!this.items || this.items.length === 0) {
      errors.push('订单必须包含至少一个材料项');
    }

    // 验证材料项
    if (this.items && this.items.length > 0) {
      this.items.forEach((item, index) => {
        if (!item.name || item.name.trim() === '') {
          errors.push(`第${index + 1}个材料项名称不能为空`);
        }
        if (!item.quantity || item.quantity <= 0) {
          errors.push(`第${index + 1}个材料项数量必须大于0`);
        }
        if (!item.price || item.price <= 0) {
          errors.push(`第${index + 1}个材料项单价必须大于0`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 计算总金额
   * @returns {number} 总金额
   */
  calculateTotalAmount() {
    if (!this.items || this.items.length === 0) {
      return 0;
    }

    return this.items.reduce((total, item) => {
      return total + (item.quantity * item.price);
    }, 0);
  }

  /**
   * 保存订单到数据库
   * @returns {Promise<Order>}
   */
  async save() {
    const validation = this.validate();
    if (!validation.isValid) {
      throw new Error(`订单数据验证失败: ${validation.errors.join(', ')}`);
    }

    // 重新计算总金额
    this.totalAmount = this.calculateTotalAmount();

    try {
      await dbConnection.beginTransaction();

      if (this.id) {
        // 更新现有订单
        await this.update();
      } else {
        // 创建新订单
        await this.create();
      }

      await dbConnection.commit();
      return this;
    } catch (error) {
      await dbConnection.rollback();
      throw error;
    }
  }

  /**
   * 创建新订单
   * @returns {Promise<void>}
   */
  async create() {
    // 生成订单ID
    this.id = this.generateOrderId();

    // 插入订单
    const orderSql = `
      INSERT INTO orders (
        id, date, customer_name, customer_contact, customer_address,
        intermediary_name, intermediary_contact, notes, total_amount
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await dbConnection.run(orderSql, [
      this.id,
      this.date,
      this.customerName,
      this.customerContact,
      this.customerAddress,
      this.intermediaryName,
      this.intermediaryContact,
      this.notes,
      this.totalAmount
    ]);

    // 插入订单项
    await this.saveOrderItems();
  }

  /**
   * 更新现有订单
   * @returns {Promise<void>}
   */
  async update() {
    // 更新订单
    const orderSql = `
      UPDATE orders SET
        date = ?, customer_name = ?, customer_contact = ?, customer_address = ?,
        intermediary_name = ?, intermediary_contact = ?, notes = ?, total_amount = ?
      WHERE id = ?
    `;

    await dbConnection.run(orderSql, [
      this.date,
      this.customerName,
      this.customerContact,
      this.customerAddress,
      this.intermediaryName,
      this.intermediaryContact,
      this.notes,
      this.totalAmount,
      this.id
    ]);

    // 删除旧的订单项
    await dbConnection.run('DELETE FROM order_items WHERE order_id = ?', [this.id]);

    // 插入新的订单项
    await this.saveOrderItems();
  }

  /**
   * 保存订单项
   * @returns {Promise<void>}
   */
  async saveOrderItems() {
    if (!this.items || this.items.length === 0) {
      return;
    }

    const itemSql = `
      INSERT INTO order_items (order_id, name, type, quantity, price, amount)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    for (const item of this.items) {
      const amount = item.quantity * item.price;
      await dbConnection.run(itemSql, [
        this.id,
        item.name,
        item.type || null,
        item.quantity,
        item.price,
        amount
      ]);
    }
  }

  /**
   * 生成订单ID
   * @returns {string}
   */
  generateOrderId() {
    const timestamp = new Date().getTime();
    const random = Math.floor(Math.random() * 1000);
    return `ORD-${timestamp}-${random}`;
  }

  /**
   * 转换为前端格式
   * @returns {Object}
   */
  toJSON() {
    return {
      id: this.id,
      date: this.date,
      customerName: this.customerName,
      customerContact: this.customerContact,
      intermediaryName: this.intermediaryName,
      intermediaryContact: this.intermediaryContact,
      notes: this.notes,
      totalAmount: this.totalAmount,
      items: this.items,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = Order;
