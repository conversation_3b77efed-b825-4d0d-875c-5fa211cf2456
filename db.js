const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const { app } = require('electron');

// 确保数据目录存在
const userDataPath = app.getPath('userData');
const dbDir = path.join(userDataPath, 'database');
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// 数据库文件路径
const dbPath = path.join(dbDir, 'matbook.db');

// 创建数据库连接
const db = new sqlite3.Database(dbPath);

// 初始化数据库
function initDatabase() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // 创建订单表
      db.run(`
        CREATE TABLE IF NOT EXISTS orders (
          id TEXT PRIMARY KEY,
          date TEXT NOT NULL,
          customer_name TEXT NOT NULL,
          customer_contact TEXT,
          intermediary_name TEXT,
          intermediary_contact TEXT,
          notes TEXT,
          total_amount REAL NOT NULL
        )
      `, (err) => {
        if (err) reject(err);
      });

      // 创建订单项表
      db.run(`
        CREATE TABLE IF NOT EXISTS order_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          order_id TEXT NOT NULL,
          name TEXT NOT NULL,
          quantity REAL NOT NULL,
          price REAL NOT NULL,
          amount REAL NOT NULL,
          FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE
        )
      `, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  });
}

// 获取所有订单
function getAllOrders() {
  return new Promise((resolve, reject) => {
    db.all(`
      SELECT * FROM orders ORDER BY date DESC
    `, [], (err, orders) => {
      if (err) {
        reject(err);
        return;
      }

      // 获取每个订单的材料项
      const promises = orders.map(order => {
        return new Promise((resolve, reject) => {
          db.all(`
            SELECT * FROM order_items WHERE order_id = ?
          `, [order.id], (err, items) => {
            if (err) {
              reject(err);
              return;
            }

            // 转换字段名称为驼峰命名法，以匹配前端代码
            const formattedItems = items.map(item => ({
              name: item.name,
              quantity: item.quantity,
              price: item.price,
              amount: item.amount
            }));

            // 转换字段名称为驼峰命名法，以匹配前端代码
            resolve({
              id: order.id,
              date: order.date,
              customerName: order.customer_name,
              customerContact: order.customer_contact,
              customerAddress: order.customer_address,
              intermediaryName: order.intermediary_name,
              intermediaryContact: order.intermediary_contact,
              notes: order.notes,
              totalAmount: order.total_amount,
              items: formattedItems
            });
          });
        });
      });

      Promise.all(promises)
        .then(formattedOrders => resolve(formattedOrders))
        .catch(err => reject(err));
    });
  });
}

// 获取单个订单
function getOrder(orderId) {
  return new Promise((resolve, reject) => {
    db.get(`
      SELECT * FROM orders WHERE id = ?
    `, [orderId], (err, order) => {
      if (err) {
        reject(err);
        return;
      }

      if (!order) {
        resolve(null);
        return;
      }

      // 获取订单的材料项
      db.all(`
        SELECT * FROM order_items WHERE order_id = ?
      `, [orderId], (err, items) => {
        if (err) {
          reject(err);
          return;
        }

        // 转换字段名称为驼峰命名法，以匹配前端代码
        const formattedItems = items.map(item => ({
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          amount: item.amount
        }));

        // 转换字段名称为驼峰命名法，以匹配前端代码
        resolve({
          id: order.id,
          date: order.date,
          customerName: order.customer_name,
          customerContact: order.customer_contact,
          customerAddress: order.customer_address,
          intermediaryName: order.intermediary_name,
          intermediaryContact: order.intermediary_contact,
          notes: order.notes,
          totalAmount: order.total_amount,
          items: formattedItems
        });
      });
    });
  });
}

// 添加订单
function addOrder(order) {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // 开始事务
      db.run('BEGIN TRANSACTION');

      // 插入订单
      db.run(`
        INSERT INTO orders (
          id, date, customer_name, customer_contact, customer_address,
          intermediary_name, intermediary_contact, notes, total_amount
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        order.id,
        order.date,
        order.customerName,
        order.customerContact,
        order.customerAddress,
        order.intermediaryName,
        order.intermediaryContact,
        order.notes,
        order.totalAmount
      ], function(err) {
        if (err) {
          db.run('ROLLBACK');
          reject(err);
          return;
        }

        // 插入订单项
        const stmt = db.prepare(`
          INSERT INTO order_items (
            order_id, name, quantity, price, amount
          ) VALUES (?, ?, ?, ?, ?)
        `);

        let hasError = false;

        order.items.forEach(item => {
          stmt.run([
            order.id,
            item.name,
            item.quantity,
            item.price,
            item.amount
          ], function(err) {
            if (err && !hasError) {
              hasError = true;
              db.run('ROLLBACK');
              reject(err);
            }
          });
        });

        stmt.finalize();

        if (!hasError) {
          // 提交事务
          db.run('COMMIT', function(err) {
            if (err) {
              db.run('ROLLBACK');
              reject(err);
            } else {
              resolve();
            }
          });
        }
      });
    });
  });
}

// 更新订单
function updateOrder(order) {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // 开始事务
      db.run('BEGIN TRANSACTION');

      // 更新订单
      db.run(`
        UPDATE orders SET
          date = ?,
          customer_name = ?,
          customer_contact = ?,
          intermediary_name = ?,
          intermediary_contact = ?,
          notes = ?,
          total_amount = ?
        WHERE id = ?
      `, [
        order.date,
        order.customerName,
        order.customerContact,
        order.intermediaryName,
        order.intermediaryContact,
        order.notes,
        order.totalAmount,
        order.id
      ], function(err) {
        if (err) {
          db.run('ROLLBACK');
          reject(err);
          return;
        }

        // 删除旧的订单项
        db.run(`
          DELETE FROM order_items WHERE order_id = ?
        `, [order.id], function(err) {
          if (err) {
            db.run('ROLLBACK');
            reject(err);
            return;
          }

          // 插入新的订单项
          const stmt = db.prepare(`
            INSERT INTO order_items (
              order_id, name, quantity, price, amount
            ) VALUES (?, ?, ?, ?, ?)
          `);

          let hasError = false;

          order.items.forEach(item => {
            stmt.run([
              order.id,
              item.name,
              item.quantity,
              item.price,
              item.amount
            ], function(err) {
              if (err && !hasError) {
                hasError = true;
                db.run('ROLLBACK');
                reject(err);
              }
            });
          });

          stmt.finalize();

          if (!hasError) {
            // 提交事务
            db.run('COMMIT', function(err) {
              if (err) {
                db.run('ROLLBACK');
                reject(err);
              } else {
                resolve();
              }
            });
          }
        });
      });
    });
  });
}

// 删除订单
function deleteOrder(orderId) {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // 开始事务
      db.run('BEGIN TRANSACTION');

      // 删除订单项
      db.run(`
        DELETE FROM order_items WHERE order_id = ?
      `, [orderId], function(err) {
        if (err) {
          db.run('ROLLBACK');
          reject(err);
          return;
        }

        // 删除订单
        db.run(`
          DELETE FROM orders WHERE id = ?
        `, [orderId], function(err) {
          if (err) {
            db.run('ROLLBACK');
            reject(err);
          } else {
            // 提交事务
            db.run('COMMIT', function(err) {
              if (err) {
                db.run('ROLLBACK');
                reject(err);
              } else {
                resolve();
              }
            });
          }
        });
      });
    });
  });
}

// 关闭数据库连接
function closeDatabase() {
  return new Promise((resolve, reject) => {
    db.close(err => {
      if (err) reject(err);
      else resolve();
    });
  });
}

module.exports = {
  initDatabase,
  getAllOrders,
  getOrder,
  addOrder,
  updateOrder,
  deleteOrder,
  closeDatabase
};