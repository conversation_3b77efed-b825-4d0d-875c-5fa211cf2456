const dbConnection = require('./connection');

/**
 * 数据库迁移管理类
 * 负责数据库表结构的创建和更新
 */
class DatabaseMigrations {
  constructor() {
    this.migrations = [
      {
        version: 1,
        name: 'create_initial_tables',
        up: this.createInitialTables.bind(this)
      },
      {
        version: 2,
        name: 'add_timestamps',
        up: this.addTimestamps.bind(this)
      },
      {
        version: 3,
        name: 'add_indexes',
        up: this.addIndexes.bind(this)
      },
      {
        version: 4,
        name: 'add_material_type_column',
        up: this.addMaterialTypeColumn.bind(this)
      },
      {
        version: 5,
        name: 'add_customer_address_column',
        up: this.addCustomerAddressColumn.bind(this)
      },
      {
        version: 6,
        name: 'create_products_table',
        up: this.createProductsTable.bind(this)
      }
    ];
  }

  /**
   * 运行所有待执行的迁移
   * @returns {Promise<void>}
   */
  async runMigrations() {
    try {
      // 确保数据库连接已初始化
      if (!dbConnection.isConnected()) {
        await dbConnection.initialize();
      }

      // 创建迁移记录表
      await this.createMigrationsTable();

      // 获取已执行的迁移
      const executedMigrations = await this.getExecutedMigrations();
      const executedVersions = executedMigrations.map(m => m.version);

      // 执行待执行的迁移
      for (const migration of this.migrations) {
        if (!executedVersions.includes(migration.version)) {
          console.log(`执行迁移: ${migration.name} (版本 ${migration.version})`);

          await dbConnection.beginTransaction();

          try {
            await migration.up();
            await this.recordMigration(migration);
            await dbConnection.commit();

            console.log(`迁移完成: ${migration.name}`);
          } catch (error) {
            await dbConnection.rollback();
            throw error;
          }
        }
      }

      console.log('所有数据库迁移执行完成');
    } catch (error) {
      console.error('数据库迁移失败:', error);
      throw error;
    }
  }

  /**
   * 创建迁移记录表
   * @returns {Promise<void>}
   */
  async createMigrationsTable() {
    const sql = `
      CREATE TABLE IF NOT EXISTS migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        version INTEGER UNIQUE NOT NULL,
        name TEXT NOT NULL,
        executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    await dbConnection.run(sql);
  }

  /**
   * 获取已执行的迁移
   * @returns {Promise<Array>}
   */
  async getExecutedMigrations() {
    const sql = 'SELECT version, name, executed_at FROM migrations ORDER BY version';
    return await dbConnection.all(sql);
  }

  /**
   * 记录已执行的迁移
   * @param {Object} migration - 迁移对象
   * @returns {Promise<void>}
   */
  async recordMigration(migration) {
    const sql = 'INSERT INTO migrations (version, name) VALUES (?, ?)';
    await dbConnection.run(sql, [migration.version, migration.name]);
  }

  /**
   * 创建初始表结构
   * @returns {Promise<void>}
   */
  async createInitialTables() {
    // 创建订单表（不包含时间戳字段，这些将在后续迁移中添加）
    const createOrdersTable = `
      CREATE TABLE IF NOT EXISTS orders (
        id TEXT PRIMARY KEY,
        date TEXT NOT NULL,
        customer_name TEXT NOT NULL,
        customer_contact TEXT,
        intermediary_name TEXT,
        intermediary_contact TEXT,
        notes TEXT,
        total_amount REAL NOT NULL
      )
    `;

    // 创建订单项表
    const createOrderItemsTable = `
      CREATE TABLE IF NOT EXISTS order_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id TEXT NOT NULL,
        name TEXT NOT NULL,
        quantity REAL NOT NULL,
        price REAL NOT NULL,
        amount REAL NOT NULL,
        FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE
      )
    `;

    await dbConnection.run(createOrdersTable);
    await dbConnection.run(createOrderItemsTable);
  }

  /**
   * 添加时间戳字段
   * @returns {Promise<void>}
   */
  async addTimestamps() {
    // 检查列是否已存在
    const ordersInfo = await dbConnection.all("PRAGMA table_info(orders)");
    const hasCreatedAt = ordersInfo.some(col => col.name === 'created_at');
    const hasUpdatedAt = ordersInfo.some(col => col.name === 'updated_at');

    // 添加 created_at 列
    if (!hasCreatedAt) {
      await dbConnection.run(`
        ALTER TABLE orders
        ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      `);
    }

    // 添加 updated_at 列
    if (!hasUpdatedAt) {
      await dbConnection.run(`
        ALTER TABLE orders
        ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      `);
    }

    // 创建触发器以自动更新 updated_at 字段
    const createUpdateTrigger = `
      CREATE TRIGGER IF NOT EXISTS update_orders_updated_at
      AFTER UPDATE ON orders
      FOR EACH ROW
      BEGIN
        UPDATE orders SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END
    `;

    await dbConnection.run(createUpdateTrigger);

    // 为订单项表添加 created_at 列
    const orderItemsInfo = await dbConnection.all("PRAGMA table_info(order_items)");
    const itemsHasCreatedAt = orderItemsInfo.some(col => col.name === 'created_at');

    if (!itemsHasCreatedAt) {
      await dbConnection.run(`
        ALTER TABLE order_items
        ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      `);
    }
  }

  /**
   * 添加索引以提高查询性能
   * @returns {Promise<void>}
   */
  async addIndexes() {
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_orders_date ON orders(date)',
      'CREATE INDEX IF NOT EXISTS idx_orders_customer_name ON orders(customer_name)',
      'CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id)',
      'CREATE INDEX IF NOT EXISTS idx_order_items_name ON order_items(name)',
      'CREATE INDEX IF NOT EXISTS idx_order_items_type ON order_items(type)',
      'CREATE INDEX IF NOT EXISTS idx_order_items_name_type ON order_items(name, type)'
    ];

    for (const indexSql of indexes) {
      await dbConnection.run(indexSql);
    }
  }

  /**
   * 添加材料类型字段
   * @returns {Promise<void>}
   */
  async addMaterialTypeColumn() {
    // 检查 order_items 表是否已有 type 列
    const orderItemsInfo = await dbConnection.all("PRAGMA table_info(order_items)");
    const hasTypeColumn = orderItemsInfo.some(col => col.name === 'type');

    if (!hasTypeColumn) {
      await dbConnection.run(`
        ALTER TABLE order_items
        ADD COLUMN type TEXT
      `);
      console.log('已添加 type 列到 order_items 表');
    }
  }

  /**
   * 添加客户地址字段
   * @returns {Promise<void>}
   */
  async addCustomerAddressColumn() {
    // 检查 orders 表是否已有 customer_address 列
    const ordersInfo = await dbConnection.all("PRAGMA table_info(orders)");
    const hasAddressColumn = ordersInfo.some(col => col.name === 'customer_address');

    if (!hasAddressColumn) {
      await dbConnection.run(`
        ALTER TABLE orders
        ADD COLUMN customer_address TEXT
      `);
      console.log('已添加 customer_address 列到 orders 表');
    }
  }

  /**
   * 创建商品表
   * @returns {Promise<void>}
   */
  async createProductsTable() {
    const createProductsTable = `
      CREATE TABLE IF NOT EXISTS products (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        category TEXT,
        price REAL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // 创建商品表
    await dbConnection.run(createProductsTable);

    // 创建商品表的索引
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)',
      'CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)',
      'CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at)'
    ];

    for (const indexSql of indexes) {
      await dbConnection.run(indexSql);
    }

    // 创建触发器以自动更新 updated_at 字段
    const createUpdateTrigger = `
      CREATE TRIGGER IF NOT EXISTS update_products_updated_at
      AFTER UPDATE ON products
      FOR EACH ROW
      BEGIN
        UPDATE products SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END
    `;

    await dbConnection.run(createUpdateTrigger);

    console.log('已创建 products 表及相关索引和触发器');
  }

  /**
   * 重置数据库（仅用于开发环境）
   * @returns {Promise<void>}
   */
  async resetDatabase() {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('不能在生产环境中重置数据库');
    }

    const tables = ['products', 'order_items', 'orders', 'migrations'];

    for (const table of tables) {
      await dbConnection.run(`DROP TABLE IF EXISTS ${table}`);
    }

    console.log('数据库已重置');
  }
}

module.exports = new DatabaseMigrations();
