const dbConnection = require('../connection');
const Order = require('../models/Order');

/**
 * 订单仓库类
 * 负责订单数据的查询和操作
 */
class OrderRepository {
  /**
   * 获取所有订单
   * @param {Object} options - 查询选项
   * @returns {Promise<Array<Order>>}
   */
  async findAll(options = {}) {
    try {
      let sql = `
        SELECT * FROM orders
        ORDER BY date DESC, created_at DESC
      `;

      const params = [];

      // 添加分页支持
      if (options.limit) {
        sql += ' LIMIT ?';
        params.push(options.limit);

        if (options.offset) {
          sql += ' OFFSET ?';
          params.push(options.offset);
        }
      }

      const orders = await dbConnection.all(sql, params);

      // 为每个订单加载材料项
      const ordersWithItems = await Promise.all(
        orders.map(async (orderData) => {
          const items = await this.findOrderItems(orderData.id);
          const order = new Order({
            ...orderData,
            items
          });
          return order;
        })
      );

      return ordersWithItems;
    } catch (error) {
      console.error('获取所有订单失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID查找订单
   * @param {string} orderId - 订单ID
   * @returns {Promise<Order|null>}
   */
  async findById(orderId) {
    try {
      const sql = 'SELECT * FROM orders WHERE id = ?';
      const orderData = await dbConnection.get(sql, [orderId]);

      if (!orderData) {
        return null;
      }

      const items = await this.findOrderItems(orderId);

      return new Order({
        ...orderData,
        items
      });
    } catch (error) {
      console.error('根据ID查找订单失败:', error);
      throw error;
    }
  }

  /**
   * 根据条件搜索订单
   * @param {Object} criteria - 搜索条件
   * @returns {Promise<Array<Order>>}
   */
  async search(criteria = {}) {
    try {
      let sql = 'SELECT * FROM orders WHERE 1=1';
      const params = [];

      // 日期范围搜索
      if (criteria.startDate) {
        sql += ' AND date >= ?';
        params.push(criteria.startDate);
      }

      if (criteria.endDate) {
        sql += ' AND date <= ?';
        params.push(criteria.endDate);
      }

      // 客户名称搜索
      if (criteria.customerName) {
        sql += ' AND customer_name LIKE ?';
        params.push(`%${criteria.customerName}%`);
      }

      // 中间商名称搜索
      if (criteria.intermediaryName) {
        sql += ' AND intermediary_name LIKE ?';
        params.push(`%${criteria.intermediaryName}%`);
      }

      // 关键词搜索（客户名称或中间商名称）
      if (criteria.keyword) {
        sql += ' AND (customer_name LIKE ? OR intermediary_name LIKE ?)';
        params.push(`%${criteria.keyword}%`, `%${criteria.keyword}%`);
      }

      sql += ' ORDER BY date DESC, created_at DESC';

      // 分页
      if (criteria.limit) {
        sql += ' LIMIT ?';
        params.push(criteria.limit);

        if (criteria.offset) {
          sql += ' OFFSET ?';
          params.push(criteria.offset);
        }
      }

      const orders = await dbConnection.all(sql, params);

      // 为每个订单加载材料项
      const ordersWithItems = await Promise.all(
        orders.map(async (orderData) => {
          const items = await this.findOrderItems(orderData.id);
          return new Order({
            ...orderData,
            items
          });
        })
      );

      return ordersWithItems;
    } catch (error) {
      console.error('搜索订单失败:', error);
      throw error;
    }
  }

  /**
   * 创建订单
   * @param {Object} orderData - 订单数据
   * @returns {Promise<Order>}
   */
  async create(orderData) {
    try {
      const order = new Order(orderData);
      return await order.save();
    } catch (error) {
      console.error('创建订单失败:', error);
      throw error;
    }
  }

  /**
   * 更新订单
   * @param {string} orderId - 订单ID
   * @param {Object} orderData - 订单数据
   * @returns {Promise<Order>}
   */
  async update(orderId, orderData) {
    try {
      const existingOrder = await this.findById(orderId);
      if (!existingOrder) {
        throw new Error('订单不存在');
      }

      // 更新订单数据
      Object.assign(existingOrder, orderData);
      existingOrder.id = orderId; // 确保ID不变

      return await existingOrder.save();
    } catch (error) {
      console.error('更新订单失败:', error);
      throw error;
    }
  }

  /**
   * 删除订单
   * @param {string} orderId - 订单ID
   * @returns {Promise<boolean>}
   */
  async delete(orderId) {
    try {
      await dbConnection.beginTransaction();

      // 删除订单项
      await dbConnection.run('DELETE FROM order_items WHERE order_id = ?', [orderId]);

      // 删除订单
      const result = await dbConnection.run('DELETE FROM orders WHERE id = ?', [orderId]);

      await dbConnection.commit();

      return result.changes > 0;
    } catch (error) {
      await dbConnection.rollback();
      console.error('删除订单失败:', error);
      throw error;
    }
  }

  /**
   * 获取订单项
   * @param {string} orderId - 订单ID
   * @returns {Promise<Array>}
   */
  async findOrderItems(orderId) {
    try {
      const sql = 'SELECT * FROM order_items WHERE order_id = ? ORDER BY id';
      const items = await dbConnection.all(sql, [orderId]);

      return items.map(item => ({
        name: item.name,
        type: item.type,
        quantity: item.quantity,
        price: item.price,
        amount: item.amount
      }));
    } catch (error) {
      console.error('获取订单项失败:', error);
      throw error;
    }
  }

  /**
   * 获取订单统计信息
   * @param {Object} criteria - 统计条件
   * @returns {Promise<Object>}
   */
  async getStatistics(criteria = {}) {
    try {
      let sql = 'SELECT COUNT(*) as count, SUM(total_amount) as total FROM orders WHERE 1=1';
      const params = [];

      if (criteria.startDate) {
        sql += ' AND date >= ?';
        params.push(criteria.startDate);
      }

      if (criteria.endDate) {
        sql += ' AND date <= ?';
        params.push(criteria.endDate);
      }

      const result = await dbConnection.get(sql, params);

      return {
        orderCount: result.count || 0,
        totalAmount: result.total || 0
      };
    } catch (error) {
      console.error('获取订单统计失败:', error);
      throw error;
    }
  }

  /**
   * 获取订单数量
   * @param {Object} criteria - 查询条件
   * @returns {Promise<number>}
   */
  async count(criteria = {}) {
    try {
      let sql = 'SELECT COUNT(*) as count FROM orders WHERE 1=1';
      const params = [];

      if (criteria.startDate) {
        sql += ' AND date >= ?';
        params.push(criteria.startDate);
      }

      if (criteria.endDate) {
        sql += ' AND date <= ?';
        params.push(criteria.endDate);
      }

      if (criteria.keyword) {
        sql += ' AND (customer_name LIKE ? OR intermediary_name LIKE ?)';
        params.push(`%${criteria.keyword}%`, `%${criteria.keyword}%`);
      }

      const result = await dbConnection.get(sql, params);
      return result.count || 0;
    } catch (error) {
      console.error('获取订单数量失败:', error);
      throw error;
    }
  }

  /**
   * 根据材料名称搜索相关的材料类型
   * @param {string} materialName - 材料名称
   * @returns {Promise<Array<string>>}
   */
  async searchMaterialTypes(materialName) {
    try {
      if (!materialName || materialName.trim() === '') {
        return [];
      }

      const sql = `
        SELECT DISTINCT type
        FROM order_items
        WHERE name LIKE ? AND type IS NOT NULL AND type != ''
        ORDER BY type
        LIMIT 10
      `;

      const params = [`%${materialName.trim()}%`];
      const results = await dbConnection.all(sql, params);

      return results.map(row => row.type);
    } catch (error) {
      console.error('搜索材料类型失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有不同的材料类型
   * @returns {Promise<Array<string>>}
   */
  async getAllMaterialTypes() {
    try {
      const sql = `
        SELECT DISTINCT type
        FROM order_items
        WHERE type IS NOT NULL AND type != ''
        ORDER BY type
      `;

      const results = await dbConnection.all(sql);
      return results.map(row => row.type);
    } catch (error) {
      console.error('获取所有材料类型失败:', error);
      throw error;
    }
  }
}

module.exports = new OrderRepository();
