<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MatBook 建材店铺记账</title>
  <!-- Tailwind CSS 通过 CDN 引入 -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Chart.js 通过 CDN 引入 -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-100">
  <!-- 登录视图 -->
  <div id="login-view" class="hidden min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-md w-96">
      <h1 class="text-2xl font-bold mb-6 text-center">MatBook 建材店铺记账</h1>
      <form id="login-form" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700">用户名</label>
          <input type="text" id="username" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">密码</label>
          <input type="password" id="password" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
        </div>
        <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          登录
        </button>
      </form>
    </div>
  </div>

  <!-- 主应用视图 -->
  <div id="app-view" class="min-h-screen flex flex-col">
    <!-- 导航栏 -->
    <nav class="bg-indigo-600 text-white shadow-md">
      <div class="max-w-7xl mx-auto px-4">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <span class="text-xl font-bold">MatBook</span>
          </div>
          <div class="flex items-center space-x-4">
            <button id="nav-dashboard" class="nav-btn px-3 py-2 rounded-md text-sm font-medium hover:bg-indigo-500">仪表盘</button>
            <button id="nav-new-order" class="nav-btn px-3 py-2 rounded-md text-sm font-medium hover:bg-indigo-500" onclick="handleNewOrderClick()">新建订单</button>
            <button id="nav-order-list" class="nav-btn px-3 py-2 rounded-md text-sm font-medium hover:bg-indigo-500">订单列表</button>
            <button id="nav-product-management" class="nav-btn px-3 py-2 rounded-md text-sm font-medium hover:bg-indigo-500">商品管理</button>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="flex-1 max-w-7xl mx-auto px-4 py-6">
      <!-- 仪表盘视图 -->
      <div id="dashboard-view" class="view-content">
        <h1 class="text-2xl font-bold mb-6">仪表盘</h1>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold text-gray-700">今日订单数</h2>
            <p id="today-order-count" class="text-3xl font-bold text-indigo-600">0</p>
          </div>
          <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold text-gray-700">今日总金额</h2>
            <p id="today-total-amount" class="text-3xl font-bold text-indigo-600">¥0.00</p>
          </div>
          <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold text-gray-700">总订单数</h2>
            <p id="total-order-count" class="text-3xl font-bold text-indigo-600">0</p>
          </div>
        </div>

        <!-- 图表 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 class="text-lg font-semibold text-gray-700 mb-4">订单趋势</h2>
          <canvas id="orders-chart" height="200"></canvas>
        </div>

        <!-- 最近订单 -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-gray-700">最近订单</h2>
            <button id="create-order-btn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
              创建新订单
            </button>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单ID</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody id="recent-orders-table" class="bg-white divide-y divide-gray-200">
                <!-- 最近订单数据将通过JavaScript动态填充 -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- 订单表单视图 -->
      <div id="order-form-view" class="view-content hidden">
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex justify-between items-center mb-6">
            <h1 id="order-form-title" class="text-2xl font-bold text-gray-900">新建订单</h1>
            <div class="flex space-x-3">
              <button id="save-order-btn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                保存订单
              </button>
              <button id="cancel-order-btn" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                取消
              </button>
            </div>
          </div>

          <!-- 订单基本信息 -->
          <div class="mb-6">
            <div class="order-info-card">
              <h3 class="order-info-card-title">订单基本信息</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="order-date" class="block text-sm font-medium text-gray-700 mb-2">订单日期 *</label>
                  <input type="date" id="order-date" class="form-control" required>
                </div>
              </div>
            </div>
          </div>

          <!-- 客户信息区域 -->
          <div class="mb-6">
            <div class="customer-info-card">
              <h3 class="customer-info-card-title">
                <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                客户信息
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="customer-name" class="block text-sm font-medium text-gray-700 mb-2">客户名称 *</label>
                  <input type="text" id="customer-name" class="form-control" placeholder="请输入客户名称" required>
                </div>
                <div>
                  <label for="customer-contact" class="block text-sm font-medium text-gray-700 mb-2">联系方式</label>
                  <input type="text" id="customer-contact" class="form-control" placeholder="请输入客户联系方式">
                </div>
                <div class="md:col-span-2">
                  <label for="customer-address" class="block text-sm font-medium text-gray-700 mb-2">客户地址</label>
                  <textarea id="customer-address" rows="3" class="form-control" placeholder="请输入客户详细地址，包括省市区县、街道门牌号等"></textarea>
                </div>
              </div>
            </div>
          </div>

          <!-- 中间商信息区域 -->
          <div class="mb-6">
            <div class="intermediary-info-card">
              <h3 class="intermediary-info-card-title">
                <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                中间商信息
                <span class="text-sm font-normal text-gray-500 ml-2">(选填)</span>
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="intermediary-name" class="block text-sm font-medium text-gray-700 mb-2">中间商名称</label>
                  <input type="text" id="intermediary-name" class="form-control" placeholder="请输入中间商名称">
                </div>
                <div>
                  <label for="intermediary-contact" class="block text-sm font-medium text-gray-700 mb-2">联系方式</label>
                  <input type="text" id="intermediary-contact" class="form-control" placeholder="请输入中间商联系方式">
                </div>
              </div>
            </div>
          </div>

          <!-- 材料项列表 -->
          <div class="mb-6">
            <div class="materials-info-card">
              <div class="flex justify-between items-center mb-4">
                <h3 class="materials-info-card-title">
                  <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                  </svg>
                  材料清单
                </h3>
                <button id="add-item-btn" type="button" class="add-material-btn-enhanced">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  添加材料项
                </button>
              </div>

              <!-- 材料项表头 -->
              <div class="grid grid-cols-12 gap-4 mb-3 text-sm font-medium text-gray-700 border-b border-gray-200 pb-2">
                <div class="col-span-3">材料名称</div>
                <div class="col-span-2">类型</div>
                <div class="col-span-2 text-center">数量</div>
                <div class="col-span-2 text-center">单价 (¥)</div>
                <div class="col-span-2 text-center">金额 (¥)</div>
                <div class="col-span-1 text-center">操作</div>
              </div>

              <!-- 材料项容器 -->
              <div id="order-items-container" class="space-y-3">
                <!-- 材料项将通过JavaScript动态添加 -->
              </div>
            </div>
          </div>

          <!-- 订单备注 -->
          <div class="mb-6">
            <div class="notes-info-card">
              <h3 class="notes-info-card-title">
                <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                订单备注
                <span class="text-sm font-normal text-gray-500 ml-2">(选填)</span>
              </h3>
              <textarea id="order-notes" rows="3" class="form-control" placeholder="请输入订单备注信息，如特殊要求、交货时间等"></textarea>
            </div>
          </div>

          <!-- 订单总计 -->
          <div class="order-total-section">
            <div class="order-total-display">
              <span class="order-total-label">
                <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
                订单总金额：
              </span>
              <span id="order-total-amount" class="order-total-amount">¥0.00</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 订单列表视图 -->
      <div id="order-list-view" class="view-content hidden">
        <h1 class="text-2xl font-bold mb-6">订单列表</h1>

        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">关键词搜索</label>
              <input type="text" id="search-keyword" placeholder="客户名称或中间商名称" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">开始日期</label>
              <input type="date" id="search-start-date" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">结束日期</label>
              <input type="date" id="search-end-date" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
            </div>
          </div>

          <div class="flex justify-between">
            <button id="search-btn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
              搜索
            </button>
            <button id="export-csv-btn" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              导出CSV
            </button>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单ID</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户名称</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">中间商名称</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总金额</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody id="orders-table" class="bg-white divide-y divide-gray-200">
                <!-- 订单数据将通过JavaScript动态填充 -->
              </tbody>
            </table>
          </div>

          <!-- 分页控件 -->
          <div class="flex justify-between items-center mt-4">
            <div>
              <span id="pagination-info">显示 1-10 条，共 0 条</span>
            </div>
            <div class="flex space-x-2">
              <button id="prev-page-btn" class="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50">上一页</button>
              <button id="next-page-btn" class="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50">下一页</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 商品管理视图 -->
      <div id="product-management-view" class="view-content hidden">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold text-gray-900">商品管理</h1>
          <button id="add-product-btn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            添加商品
          </button>
        </div>

        <!-- 搜索和过滤 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">商品名称</label>
              <input type="text" id="product-search-name" placeholder="输入商品名称" class="form-control">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">商品分类</label>
              <select id="product-search-category" class="form-control">
                <option value="">全部分类</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">最低价格</label>
              <input type="number" id="product-search-min-price" placeholder="0.00" min="0" step="0.01" class="form-control">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">最高价格</label>
              <input type="number" id="product-search-max-price" placeholder="999999.99" min="0" step="0.01" class="form-control">
            </div>
          </div>

          <div class="flex justify-between">
            <button id="search-products-btn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
              搜索商品
            </button>
            <button id="export-products-csv-btn" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              导出CSV
            </button>
          </div>
        </div>

        <!-- 商品列表 -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商品ID</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商品名称</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">价格</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody id="products-table" class="bg-white divide-y divide-gray-200">
                <!-- 商品数据将通过JavaScript动态填充 -->
              </tbody>
            </table>
          </div>

          <!-- 分页控件 -->
          <div class="flex justify-between items-center mt-4">
            <div>
              <span id="products-pagination-info">显示 1-10 条，共 0 条</span>
            </div>
            <div class="flex space-x-2">
              <button id="products-prev-page-btn" class="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50">上一页</button>
              <button id="products-next-page-btn" class="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50">下一页</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 商品表单视图 -->
      <div id="product-form-view" class="view-content hidden">
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex justify-between items-center mb-6">
            <h1 id="product-form-title" class="text-2xl font-bold text-gray-900">添加商品</h1>
            <div class="flex space-x-3">
              <button id="save-product-btn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                保存商品
              </button>
              <button id="cancel-product-btn" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                取消
              </button>
            </div>
          </div>

          <!-- 商品表单 -->
          <form id="product-form" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="product-name" class="block text-sm font-medium text-gray-700 mb-2">
                  商品名称 <span class="text-red-500">*</span>
                </label>
                <input type="text" id="product-name" class="form-control" placeholder="请输入商品名称" required maxlength="100">
                <p class="mt-1 text-sm text-gray-500">最多100个字符</p>
              </div>
              <div>
                <label for="product-category" class="block text-sm font-medium text-gray-700 mb-2">商品分类</label>
                <input type="text" id="product-category" class="form-control" placeholder="请输入或选择商品分类" maxlength="50">
                <p class="mt-1 text-sm text-gray-500">最多50个字符</p>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="product-price" class="block text-sm font-medium text-gray-700 mb-2">商品价格</label>
                <div class="relative">
                  <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
                  <input type="number" id="product-price" class="form-control pl-8" placeholder="0.00" min="0" step="0.01">
                </div>
                <p class="mt-1 text-sm text-gray-500">可选，留空表示价格待定</p>
              </div>
            </div>

            <div>
              <label for="product-description" class="block text-sm font-medium text-gray-700 mb-2">商品描述</label>
              <textarea id="product-description" rows="4" class="form-control" placeholder="请输入商品描述信息" maxlength="500"></textarea>
              <p class="mt-1 text-sm text-gray-500">最多500个字符</p>
            </div>
          </form>
        </div>
      </div>
    </main>
  </div>

  <!-- 脚本 -->
  <!-- 工具类 -->
  <script src="src/renderer/utils/StateManager.js"></script>
  <script src="src/renderer/utils/ViewManager.js"></script>

  <!-- 服务层 -->
  <script src="src/renderer/services/OrderService.js"></script>
  <script src="src/renderer/services/ProductService.js"></script>

  <!-- 主应用脚本 -->
  <script src="app.js"></script>

  <!-- 测试脚本（开发环境） -->
  <script src="test-refactored-app.js"></script>
</body>
</html>

