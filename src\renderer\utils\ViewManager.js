/**
 * 视图管理器
 * 负责视图切换和DOM操作
 */
class ViewManager {
  constructor() {
    this.views = {
      'dashboard': 'dashboard-view',
      'order-form': 'order-form-view',
      'order-list': 'order-list-view',
      'product-management': 'product-management-view',
      'product-form': 'product-form-view'
    };

    this.currentView = null;
    this.stateManager = window.stateManager;

    // 监听状态变化
    this.stateManager.subscribe('currentView', (newView) => {
      this.switchView(newView);
    });
  }

  /**
   * 初始化视图管理器
   */
  initialize() {
    this.bindNavigationEvents();
    this.showView('dashboard');
  }

  /**
   * 绑定导航事件
   */
  bindNavigationEvents() {
    // 仪表盘按钮
    const dashboardBtn = document.getElementById('nav-dashboard');
    if (dashboardBtn) {
      dashboardBtn.addEventListener('click', () => {
        this.stateManager.setState('currentView', 'dashboard');
      });
    }

    // 新建订单按钮
    const newOrderBtn = document.getElementById('nav-new-order');
    if (newOrderBtn) {
      newOrderBtn.addEventListener('click', () => {
        this.stateManager.setState('currentView', 'order-form');
        this.stateManager.setState('currentOrder', null); // 清除当前订单（新建模式）
      });
    }

    // 订单列表按钮
    const orderListBtn = document.getElementById('nav-order-list');
    if (orderListBtn) {
      orderListBtn.addEventListener('click', () => {
        this.stateManager.setState('currentView', 'order-list');
      });
    }

    // 商品管理按钮
    const productManagementBtn = document.getElementById('nav-product-management');
    if (productManagementBtn) {
      productManagementBtn.addEventListener('click', () => {
        this.stateManager.setState('currentView', 'product-management');
      });
    }

    // 仪表盘中的创建订单按钮
    const createOrderBtn = document.getElementById('create-order-btn');
    if (createOrderBtn) {
      createOrderBtn.addEventListener('click', () => {
        this.stateManager.setState('currentView', 'order-form');
        this.stateManager.setState('currentOrder', null);
      });
    }
  }

  /**
   * 切换视图
   * @param {string} viewName - 视图名称
   */
  switchView(viewName) {
    if (this.currentView === viewName) {
      return;
    }

    console.log(`切换视图: ${this.currentView} -> ${viewName}`);

    // 隐藏所有视图
    this.hideAllViews();

    // 显示目标视图
    this.showView(viewName);

    // 更新导航状态
    this.updateNavigationState(viewName);

    // 更新当前视图
    this.currentView = viewName;

    // 触发视图切换事件
    this.onViewChanged(viewName);
  }

  /**
   * 隐藏所有视图
   */
  hideAllViews() {
    Object.values(this.views).forEach(viewId => {
      const viewElement = document.getElementById(viewId);
      if (viewElement) {
        viewElement.classList.add('hidden');
        viewElement.style.display = 'none';
      }
    });
  }

  /**
   * 显示指定视图
   * @param {string} viewName - 视图名称
   */
  showView(viewName) {
    const viewId = this.views[viewName];
    if (!viewId) {
      console.error(`未知的视图: ${viewName}`);
      return;
    }

    const viewElement = document.getElementById(viewId);
    if (viewElement) {
      viewElement.classList.remove('hidden');
      viewElement.style.display = 'block';
      console.log(`显示视图: ${viewName} (${viewId})`);
    } else {
      console.error(`找不到视图元素: ${viewId}`);
    }
  }

  /**
   * 更新导航状态
   * @param {string} activeView - 当前活动视图
   */
  updateNavigationState(activeView) {
    // 移除所有导航按钮的活动状态
    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.classList.remove('active');
    });

    // 添加当前视图对应按钮的活动状态
    let activeButtonId;
    switch (activeView) {
      case 'dashboard':
        activeButtonId = 'nav-dashboard';
        break;
      case 'order-form':
        activeButtonId = 'nav-new-order';
        break;
      case 'order-list':
        activeButtonId = 'nav-order-list';
        break;
      case 'product-management':
      case 'product-form':
        activeButtonId = 'nav-product-management';
        break;
    }

    if (activeButtonId) {
      const activeButton = document.getElementById(activeButtonId);
      if (activeButton) {
        activeButton.classList.add('active');
      }
    }
  }

  /**
   * 视图切换完成后的回调
   * @param {string} viewName - 新视图名称
   */
  onViewChanged(viewName) {
    switch (viewName) {
      case 'dashboard':
        this.onDashboardViewShown();
        break;
      case 'order-form':
        this.onOrderFormViewShown();
        break;
      case 'order-list':
        this.onOrderListViewShown();
        break;
      case 'product-management':
        this.onProductManagementViewShown();
        break;
      case 'product-form':
        this.onProductFormViewShown();
        break;
    }
  }

  /**
   * 仪表盘视图显示时的处理
   */
  onDashboardViewShown() {
    // 刷新仪表盘数据
    if (window.dashboardController) {
      window.dashboardController.refreshData();
    }
  }

  /**
   * 订单表单视图显示时的处理
   */
  onOrderFormViewShown() {
    // 初始化订单表单
    const currentOrder = this.stateManager.getState('currentOrder');
    if (currentOrder) {
      // 编辑模式 - 加载订单数据
      if (window.loadOrderToForm) {
        window.loadOrderToForm(currentOrder);
      }
    } else {
      // 新建模式 - 重置表单
      if (window.resetOrderForm) {
        window.resetOrderForm();
      }
      // 确保至少有一个材料项
      if (window.addOrderItem) {
        const container = document.getElementById('order-items-container');
        if (container && container.children.length === 0) {
          window.addOrderItem();
        }
      }
    }
  }

  /**
   * 订单列表视图显示时的处理
   */
  onOrderListViewShown() {
    // 刷新订单列表
    if (window.searchOrders) {
      window.searchOrders();
    } else {
      // 如果搜索函数不存在，直接更新表格
      const orders = this.stateManager.getState('orders') || [];
      if (window.updateOrderListTable) {
        window.updateOrderListTable(orders);
      }
    }
  }

  /**
   * 商品管理视图显示时的处理
   */
  onProductManagementViewShown() {
    // 刷新商品列表
    if (window.searchProducts) {
      window.searchProducts();
    } else {
      // 如果搜索函数不存在，直接更新表格
      const products = this.stateManager.getState('products') || [];
      if (window.updateProductListTable) {
        window.updateProductListTable(products);
      }
    }

    // 加载商品分类选项
    if (window.loadProductCategories) {
      window.loadProductCategories();
    }
  }

  /**
   * 商品表单视图显示时的处理
   */
  onProductFormViewShown() {
    // 初始化商品表单
    const currentProduct = this.stateManager.getState('currentProduct');
    if (currentProduct) {
      // 编辑模式 - 加载商品数据
      if (window.loadProductToForm) {
        window.loadProductToForm(currentProduct);
      }
    } else {
      // 新建模式 - 重置表单
      if (window.resetProductForm) {
        window.resetProductForm();
      }
    }
  }

  /**
   * 显示加载状态
   * @param {string} viewName - 视图名称
   * @param {boolean} loading - 是否加载中
   */
  setViewLoading(viewName, loading) {
    const viewId = this.views[viewName];
    if (!viewId) return;

    const viewElement = document.getElementById(viewId);
    if (!viewElement) return;

    if (loading) {
      viewElement.classList.add('loading');
      // 可以添加加载动画
    } else {
      viewElement.classList.remove('loading');
    }
  }

  /**
   * 显示错误消息
   * @param {string} message - 错误消息
   * @param {string} type - 消息类型 ('error', 'warning', 'success', 'info')
   */
  showMessage(message, type = 'info') {
    // 创建消息元素
    const messageElement = document.createElement('div');
    messageElement.className = `message message-${type}`;
    messageElement.textContent = message;

    // 添加样式
    messageElement.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 4px;
      color: white;
      font-weight: 500;
      z-index: 10000;
      max-width: 400px;
      word-wrap: break-word;
      animation: slideInRight 0.3s ease-out;
    `;

    // 根据类型设置背景色
    switch (type) {
      case 'error':
        messageElement.style.backgroundColor = '#ef4444';
        break;
      case 'warning':
        messageElement.style.backgroundColor = '#f59e0b';
        break;
      case 'success':
        messageElement.style.backgroundColor = '#10b981';
        break;
      default:
        messageElement.style.backgroundColor = '#3b82f6';
    }

    // 添加到页面
    document.body.appendChild(messageElement);

    // 自动移除
    setTimeout(() => {
      if (messageElement.parentNode) {
        messageElement.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
          if (messageElement.parentNode) {
            messageElement.parentNode.removeChild(messageElement);
          }
        }, 300);
      }
    }, 3000);
  }

  /**
   * 确认对话框
   * @param {string} message - 确认消息
   * @returns {boolean} 用户选择
   */
  confirm(message) {
    return confirm(message);
  }

  /**
   * 获取当前视图
   * @returns {string} 当前视图名称
   */
  getCurrentView() {
    return this.currentView;
  }
}

// 导出单例实例
window.viewManager = new ViewManager();
