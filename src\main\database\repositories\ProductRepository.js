const dbConnection = require('../connection');
const Product = require('../models/Product');

/**
 * 商品仓储类
 * 负责商品数据的数据库操作
 */
class ProductRepository {
  /**
   * 获取所有商品
   * @param {Object} options - 查询选项
   * @returns {Promise<Array<Product>>}
   */
  async findAll(options = {}) {
    try {
      let sql = 'SELECT * FROM products';
      const params = [];
      const conditions = [];

      // 添加搜索条件
      if (options.search) {
        conditions.push('(name LIKE ? OR category LIKE ? OR description LIKE ?)');
        const searchTerm = `%${options.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      // 添加分类过滤
      if (options.category) {
        conditions.push('category = ?');
        params.push(options.category);
      }

      // 添加价格范围过滤
      if (options.minPrice !== undefined) {
        conditions.push('price >= ?');
        params.push(options.minPrice);
      }

      if (options.maxPrice !== undefined) {
        conditions.push('price <= ?');
        params.push(options.maxPrice);
      }

      // 构建WHERE子句
      if (conditions.length > 0) {
        sql += ' WHERE ' + conditions.join(' AND ');
      }

      // 添加排序
      const orderBy = options.orderBy || 'created_at';
      const orderDirection = options.orderDirection || 'DESC';
      sql += ` ORDER BY ${orderBy} ${orderDirection}`;

      // 添加分页
      if (options.limit) {
        sql += ' LIMIT ?';
        params.push(options.limit);

        if (options.offset) {
          sql += ' OFFSET ?';
          params.push(options.offset);
        }
      }

      const records = await dbConnection.all(sql, params);
      return records.map(record => Product.fromRecord(record));
    } catch (error) {
      console.error('获取商品列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取商品
   * @param {string} productId - 商品ID
   * @returns {Promise<Product|null>}
   */
  async findById(productId) {
    try {
      const sql = 'SELECT * FROM products WHERE id = ?';
      const record = await dbConnection.get(sql, [productId]);

      if (!record) {
        return null;
      }

      return Product.fromRecord(record);
    } catch (error) {
      console.error('根据ID获取商品失败:', error);
      throw error;
    }
  }

  /**
   * 根据名称获取商品
   * @param {string} name - 商品名称
   * @returns {Promise<Product|null>}
   */
  async findByName(name) {
    try {
      const sql = 'SELECT * FROM products WHERE name = ?';
      const record = await dbConnection.get(sql, [name]);

      if (!record) {
        return null;
      }

      return Product.fromRecord(record);
    } catch (error) {
      console.error('根据名称获取商品失败:', error);
      throw error;
    }
  }

  /**
   * 搜索商品
   * @param {Object} criteria - 搜索条件
   * @returns {Promise<Object>}
   */
  async search(criteria = {}) {
    try {
      const options = {
        search: criteria.keyword,
        category: criteria.category,
        minPrice: criteria.minPrice,
        maxPrice: criteria.maxPrice,
        orderBy: criteria.orderBy || 'created_at',
        orderDirection: criteria.orderDirection || 'DESC',
        limit: criteria.limit || 50,
        offset: criteria.offset || 0
      };

      const products = await this.findAll(options);

      // 获取总数
      const totalCount = await this.count(options);

      return {
        products,
        totalCount,
        hasMore: (options.offset + options.limit) < totalCount
      };
    } catch (error) {
      console.error('搜索商品失败:', error);
      throw error;
    }
  }

  /**
   * 获取商品总数
   * @param {Object} options - 查询选项
   * @returns {Promise<number>}
   */
  async count(options = {}) {
    try {
      let sql = 'SELECT COUNT(*) as count FROM products';
      const params = [];
      const conditions = [];

      // 添加搜索条件
      if (options.search) {
        conditions.push('(name LIKE ? OR category LIKE ? OR description LIKE ?)');
        const searchTerm = `%${options.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      // 添加分类过滤
      if (options.category) {
        conditions.push('category = ?');
        params.push(options.category);
      }

      // 添加价格范围过滤
      if (options.minPrice !== undefined) {
        conditions.push('price >= ?');
        params.push(options.minPrice);
      }

      if (options.maxPrice !== undefined) {
        conditions.push('price <= ?');
        params.push(options.maxPrice);
      }

      // 构建WHERE子句
      if (conditions.length > 0) {
        sql += ' WHERE ' + conditions.join(' AND ');
      }

      const result = await dbConnection.get(sql, params);
      return result.count;
    } catch (error) {
      console.error('获取商品总数失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有商品分类
   * @returns {Promise<Array<string>>}
   */
  async getCategories() {
    try {
      const sql = `
        SELECT DISTINCT category 
        FROM products 
        WHERE category IS NOT NULL AND category != '' 
        ORDER BY category
      `;
      const records = await dbConnection.all(sql);
      return records.map(record => record.category);
    } catch (error) {
      console.error('获取商品分类失败:', error);
      throw error;
    }
  }

  /**
   * 创建商品
   * @param {Object} productData - 商品数据
   * @returns {Promise<Product>}
   */
  async create(productData) {
    try {
      const product = new Product(productData);
      await product.save();
      return product;
    } catch (error) {
      console.error('创建商品失败:', error);
      throw error;
    }
  }

  /**
   * 更新商品
   * @param {string} productId - 商品ID
   * @param {Object} productData - 商品数据
   * @returns {Promise<Product>}
   */
  async update(productId, productData) {
    try {
      const product = await this.findById(productId);
      if (!product) {
        throw new Error('商品不存在');
      }

      // 更新商品属性
      Object.assign(product, productData);
      await product.save();
      return product;
    } catch (error) {
      console.error('更新商品失败:', error);
      throw error;
    }
  }

  /**
   * 删除商品
   * @param {string} productId - 商品ID
   * @returns {Promise<boolean>}
   */
  async delete(productId) {
    try {
      const product = await this.findById(productId);
      if (!product) {
        return false;
      }

      await product.delete();
      return true;
    } catch (error) {
      console.error('删除商品失败:', error);
      throw error;
    }
  }

  /**
   * 检查商品名称是否已存在
   * @param {string} name - 商品名称
   * @param {string} excludeId - 排除的商品ID（用于更新时检查）
   * @returns {Promise<boolean>}
   */
  async isNameExists(name, excludeId = null) {
    try {
      let sql = 'SELECT COUNT(*) as count FROM products WHERE name = ?';
      const params = [name];

      if (excludeId) {
        sql += ' AND id != ?';
        params.push(excludeId);
      }

      const result = await dbConnection.get(sql, params);
      return result.count > 0;
    } catch (error) {
      console.error('检查商品名称是否存在失败:', error);
      throw error;
    }
  }
}

module.exports = new ProductRepository();
