const OrderRepository = require('../database/repositories/OrderRepository');
const { EventEmitter } = require('events');

/**
 * 订单服务类
 * 负责订单业务逻辑处理
 */
class OrderService extends EventEmitter {
  constructor() {
    super();
    this.repository = OrderRepository;
  }

  /**
   * 获取所有订单
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>}
   */
  async getAllOrders(options = {}) {
    try {
      const orders = await this.repository.findAll(options);

      return {
        success: true,
        orders: orders.map(order => order.toJSON())
      };
    } catch (error) {
      console.error('获取所有订单失败:', error);
      return {
        success: false,
        message: error.message,
        orders: []
      };
    }
  }

  /**
   * 根据ID获取订单
   * @param {string} orderId - 订单ID
   * @returns {Promise<Object>}
   */
  async getOrderById(orderId) {
    try {
      if (!orderId) {
        throw new Error('订单ID不能为空');
      }

      const order = await this.repository.findById(orderId);

      if (!order) {
        return {
          success: false,
          message: '订单不存在',
          order: null
        };
      }

      return {
        success: true,
        order: order.toJSON()
      };
    } catch (error) {
      console.error('获取订单失败:', error);
      return {
        success: false,
        message: error.message,
        order: null
      };
    }
  }

  /**
   * 搜索订单
   * @param {Object} criteria - 搜索条件
   * @returns {Promise<Object>}
   */
  async searchOrders(criteria = {}) {
    try {
      const orders = await this.repository.search(criteria);
      const totalCount = await this.repository.count(criteria);

      return {
        success: true,
        orders: orders.map(order => order.toJSON()),
        totalCount,
        page: Math.floor((criteria.offset || 0) / (criteria.limit || 10)) + 1,
        pageSize: criteria.limit || 10
      };
    } catch (error) {
      console.error('搜索订单失败:', error);
      return {
        success: false,
        message: error.message,
        orders: [],
        totalCount: 0
      };
    }
  }

  /**
   * 创建订单
   * @param {Object} orderData - 订单数据
   * @returns {Promise<Object>}
   */
  async createOrder(orderData) {
    try {
      // 数据预处理
      const processedData = this.preprocessOrderData(orderData);

      // 创建订单
      const order = await this.repository.create(processedData);

      // 发送事件
      this.emit('order-created', order.toJSON());

      return {
        success: true,
        order: order.toJSON(),
        message: '订单创建成功'
      };
    } catch (error) {
      console.error('创建订单失败:', error);
      return {
        success: false,
        message: error.message,
        order: null
      };
    }
  }

  /**
   * 更新订单
   * @param {string} orderId - 订单ID
   * @param {Object} orderData - 订单数据
   * @returns {Promise<Object>}
   */
  async updateOrder(orderId, orderData) {
    try {
      if (!orderId) {
        throw new Error('订单ID不能为空');
      }

      // 数据预处理
      const processedData = this.preprocessOrderData(orderData);

      // 更新订单
      const order = await this.repository.update(orderId, processedData);

      // 发送事件
      this.emit('order-updated', order.toJSON());

      return {
        success: true,
        order: order.toJSON(),
        message: '订单更新成功'
      };
    } catch (error) {
      console.error('更新订单失败:', error);
      return {
        success: false,
        message: error.message,
        order: null
      };
    }
  }

  /**
   * 删除订单
   * @param {string} orderId - 订单ID
   * @returns {Promise<Object>}
   */
  async deleteOrder(orderId) {
    try {
      if (!orderId) {
        throw new Error('订单ID不能为空');
      }

      // 获取订单信息（用于事件）
      const orderResult = await this.getOrderById(orderId);

      // 删除订单
      const deleted = await this.repository.delete(orderId);

      if (!deleted) {
        return {
          success: false,
          message: '订单不存在或删除失败'
        };
      }

      // 发送事件
      if (orderResult.success) {
        this.emit('order-deleted', orderResult.order);
      }

      return {
        success: true,
        message: '订单删除成功'
      };
    } catch (error) {
      console.error('删除订单失败:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * 获取订单统计信息
   * @param {Object} criteria - 统计条件
   * @returns {Promise<Object>}
   */
  async getOrderStatistics(criteria = {}) {
    try {
      const stats = await this.repository.getStatistics(criteria);

      return {
        success: true,
        statistics: stats
      };
    } catch (error) {
      console.error('获取订单统计失败:', error);
      return {
        success: false,
        message: error.message,
        statistics: {
          orderCount: 0,
          totalAmount: 0
        }
      };
    }
  }

  /**
   * 获取今日订单统计
   * @returns {Promise<Object>}
   */
  async getTodayStatistics() {
    const today = new Date().toISOString().split('T')[0];
    return await this.getOrderStatistics({
      startDate: today,
      endDate: today
    });
  }

  /**
   * 根据材料名称搜索相关的材料类型
   * @param {string} materialName - 材料名称
   * @returns {Promise<Object>}
   */
  async searchMaterialTypes(materialName) {
    try {
      const types = await this.repository.searchMaterialTypes(materialName);

      return {
        success: true,
        types
      };
    } catch (error) {
      console.error('搜索材料类型失败:', error);
      return {
        success: false,
        message: error.message,
        types: []
      };
    }
  }

  /**
   * 获取所有材料类型
   * @returns {Promise<Object>}
   */
  async getAllMaterialTypes() {
    try {
      const types = await this.repository.getAllMaterialTypes();

      return {
        success: true,
        types
      };
    } catch (error) {
      console.error('获取所有材料类型失败:', error);
      return {
        success: false,
        message: error.message,
        types: []
      };
    }
  }

  /**
   * 预处理订单数据
   * @param {Object} orderData - 原始订单数据
   * @returns {Object} 处理后的订单数据
   */
  preprocessOrderData(orderData) {
    const processed = { ...orderData };

    // 清理字符串字段
    if (processed.customerName) {
      processed.customerName = processed.customerName.trim();
    }

    if (processed.customerContact) {
      processed.customerContact = processed.customerContact.trim();
    }

    if (processed.intermediaryName) {
      processed.intermediaryName = processed.intermediaryName.trim();
    }

    if (processed.intermediaryContact) {
      processed.intermediaryContact = processed.intermediaryContact.trim();
    }

    if (processed.notes) {
      processed.notes = processed.notes.trim();
    }

    // 处理材料项
    if (processed.items && Array.isArray(processed.items)) {
      processed.items = processed.items.map(item => ({
        name: item.name ? item.name.trim() : '',
        type: item.type ? item.type.trim() : '',
        quantity: parseFloat(item.quantity) || 0,
        price: parseFloat(item.price) || 0,
        amount: parseFloat(item.quantity || 0) * parseFloat(item.price || 0)
      })).filter(item => item.name && item.quantity > 0 && item.price > 0);
    }

    return processed;
  }

  /**
   * 验证订单数据
   * @param {Object} orderData - 订单数据
   * @returns {Object} 验证结果
   */
  validateOrderData(orderData) {
    const errors = [];

    if (!orderData.date) {
      errors.push('订单日期不能为空');
    }

    if (!orderData.customerName || orderData.customerName.trim() === '') {
      errors.push('客户名称不能为空');
    }

    if (!orderData.items || !Array.isArray(orderData.items) || orderData.items.length === 0) {
      errors.push('订单必须包含至少一个材料项');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

module.exports = new OrderService();
